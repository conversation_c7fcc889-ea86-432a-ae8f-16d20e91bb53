<template>
  <div class="official-announcement-template">
    <!-- 句子填空式描述区域 -->
    <div class="sentence-template-area">
      <div class="sentence-template">
        根据以下信息，写一篇
        <span class="form-field-inline">
          <el-select
            v-model="formData.subjectContent"
            placeholder="请选择"
            size="small"
            class="inline-select"
          >
            <el-option label="通知" value="通知" />
            <el-option label="公告" value="公告" />
            <el-option label="通报" value="通报" />
            <el-option label="演讲稿" value="演讲稿" />
          </el-select>
        </span>，格式参考文档
        <span class="form-field-inline">
          <div class="inline-file-upload">
            <el-tag
              v-for="file in formData.referenceTemplate"
              class="file-tag inline-tag clickable-file-tag"
              :key="file.fileId"
              @close="removeTemplateFile(file)"
              @click.stop="previewFile(file.fileId, file.fileName)"
              closable
              size="small"
              :title="`${file.fileName} - 点击预览文件`"
            >
              {{ truncateFileName(file.fileName, 8) }}
            </el-tag>
            <el-upload
              v-if="formData.referenceTemplate.length < 3"
              action="/api/v1/public/file/upload"
              :data="uploadData"
              :on-success="handleTemplateUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
              :file-list="formData.referenceTemplate"
              :limit="3"
              list-type="text"
              :show-file-list="false"
              class="inline-upload-btn"
            >
              <el-button size="small" type="primary" text class="inline-upload-text-btn">选择文件</el-button>
            </el-upload>
          </div>
        </span>，发文单位
        <span class="form-field-inline">
          <el-input
            v-model="formData.issuingUnit"
            placeholder="输入发文单位"
            size="small"
            class="inline-input"
          />
        </span>，主送机关
        <span class="form-field-inline">
          <el-input
            v-model="formData.toPrimary"
            placeholder="输入主送机关"
            size="small"
            class="inline-input"
          />
        </span>，发文字号
        <span class="form-field-inline">
          <el-input
            v-model="formData.documentNumber"
            placeholder="输入发文字号"
            size="small"
            class="inline-input"
          />
        </span>，标题
        <span class="form-field-inline">
          <el-input
            v-model="formData.title"
            placeholder="输入公文公告标题"
            size="small"
            class="inline-input"
          />
        </span>，发布日期
        <span class="form-field-inline">
          <el-date-picker
            v-model="formData.releaseDate"
            type="date"
            placeholder="选择发布日期"
            size="small"
            class="inline-date-picker"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </span>，成文日期
        <span class="form-field-inline">
          <el-date-picker
            v-model="formData.createDate"
            type="date"
            placeholder="选择成文日期"
            size="small"
            class="inline-date-picker"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </span>，内容依据文档
        <span class="form-field-inline">
          <div class="inline-file-upload">
            <el-tag
              v-for="file in formData.referenceMaterials"
              class="file-tag inline-tag reference-file-tag clickable-file-tag"
              :key="file.fileId"
              @close="removeReferenceFile(file)"
              @click.stop="previewFile(file.fileId, file.fileName)"
              closable
              size="small"
              :title="`${file.fileName} - 点击预览文件`"
            >
              {{ truncateFileName(file.fileName, 8) }}
            </el-tag>
            <el-upload
              v-if="formData.referenceMaterials.length < 3"
              action="/api/v1/public/file/upload"
              :data="uploadData"
              :on-success="handleReferenceUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
              :file-list="[]"
              :limit="3"
              :show-file-list="false"
              class="inline-upload-btn"
            >
              <el-button size="small" type="primary" text class="inline-upload-text-btn">选择文件</el-button>
            </el-upload>
          </div>
        </span>。
      </div>
    </div>



    <!-- 内容要求输入区域 -->
    <div class="input-area">
      <textarea
        v-model="formData.contentRequirements"
        placeholder="请输入内容要求"
        class="p-2 w-full text-gray-700 align-top outline-none resize-none input-textarea announcement-textarea"
        @keyup.enter.prevent="handleEnterPress"
        rows="2"
      ></textarea>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center m-4">
          <div class="px-2 text-xs text-gray-400">内容由 AI 生成，请仔细甄别</div>
        </div>
        <div class="flex gap-2 items-center m-4 text-gray-500 func-container">
          <!-- 发送按钮 -->
          <button
            @click="handleSend"
            class="p-2 text-white rounded-full bg-primary hover:bg-primary-middle"
            :disabled="isLoading || !canSend"
          >
            <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
            <svg v-else class="animate-spin" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'OfficialAnnouncementTemplate',
  props: {
    userId: {
      type: String,
      required: true
    },
    agentId: {
      type: String,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['send-message'],
  data() {
    return {
      formData: {
        title: '',
        subjectContent: '',
        issuingUnit: '',
        toPrimary: '',
        documentNumber: '',
        releaseDate: '',
        createDate: '',
        contentRequirements: '',
        referenceMaterials: [],
        referenceTemplate: []
      },
      hasAutoFilled: false // 防止重复自动填入
    }
  },
  mounted() {
    // 组件加载完成后自动填入预设值
    this.$nextTick(() => {
      // 延迟执行，确保组件完全加载
      setTimeout(() => {
        this.autoFillPresetValues()
      }, 500)
    })
  },
  computed: {
    uploadData() {
      return {
        userId: this.userId,
        agentId: this.agentId
      }
    },
    canSend() {
      return this.formData.title && this.formData.contentRequirements
    }
  },
  methods: {
    /**
     * 自动填入预设值（仅在首次加载时执行）
     */
    async autoFillPresetValues() {
      // 检查是否已经有数据，避免覆盖用户输入
      if (this.hasAutoFilled ||
          this.formData.title ||
          this.formData.contentRequirements ||
          this.formData.referenceMaterials.length > 0) {
        console.log('跳过自动填入：已有数据或已执行过自动填入')
        return
      }

      this.hasAutoFilled = true
      await this.fillPresetValues()
    },

    /**
     * 填入预设值
     */
    async fillPresetValues() {
      try {
        // 显示加载提示
        const loadingMessage = ElMessage({
          message: '正在加载预设模板...',
          type: 'info',
          duration: 0,
          showClose: false
        })

        // 设置预设表单值
        this.formData = {
          ...this.formData,
          title: '关于加强政务公开工作的公告',
          subjectContent: '公告',
          issuingUnit: '广西壮族自治区人民政府办公厅',
          toPrimary: '各市、县人民政府，自治区人民政府各组成部门、各直属机构',
          documentNumber: '桂政办公告〔2024〕001号',
          releaseDate: '2024-12-30',
          createDate: '2024-12-28',
          contentRequirements: '请根据政务公开相关要求，起草一份关于加强政务公开工作的公告。\n\n要求：\n1. 明确政务公开的重要意义\n2. 详细说明公开的内容和范围\n3. 规范公开的方式和程序\n4. 强调监督和保障措施'
        }

        // 关闭加载提示
        loadingMessage.close()
        ElMessage.success('模板加载完成')

      } catch (error) {
        console.error('模板加载失败:', error)
        ElMessage.error('模板加载失败: ' + error.message)
      }
    },

    handleSend() {
      if (!this.canSend) {
        ElMessage.warning('请填写标题和内容要求')
        return
      }

      // 只传递表单数据，让父组件来处理
      this.$emit('send-message', { ...this.formData })
    },

    handleEnterPress(event) {
      if (event.shiftKey) {
        return
      }
      this.handleSend()
    },

    handleTemplateUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceTemplate.push(fileInfo)
        ElMessage.success('格式参考文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleReferenceUploadSuccess(response, file) {
      if (response.code === 200) {
        const { type, extension } = this.getFileType(file.name)
        const fileInfo = {
          fileId: response.data.fileId,
          fileType: type,
          extension: extension,
          fileName: file.name
        }
        this.formData.referenceMaterials.push(fileInfo)
        ElMessage.success('内容依据文档上传成功')
      } else {
        ElMessage.error(response.msg || '文件上传失败')
      }
    },

    handleUploadError(error) {
      console.error('文件上传失败:', error)
      ElMessage.error('文件上传失败，请重试')
    },

    beforeUpload(file) {
      const isValidType = this.isValidFileType(file.type, file.name)
      const isValidSize = file.size / 1024 / 1024 < 50 // 50MB

      if (!isValidType) {
        ElMessage.error('不支持的文件格式')
        return false
      }

      if (!isValidSize) {
        ElMessage.error('文件大小不能超过 50MB')
        return false
      }

      return true
    },

    removeTemplateFile(file) {
      const index = this.formData.referenceTemplate.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceTemplate.splice(index, 1)
      }
    },

    removeReferenceFile(file) {
      const index = this.formData.referenceMaterials.findIndex(f => f.fileId === file.fileId)
      if (index > -1) {
        this.formData.referenceMaterials.splice(index, 1)
      }
    },

    previewFile(fileId, fileName) {
      // TODO: 实现文件预览功能
      console.log('预览文件:', fileId, fileName)
      ElMessage.info('文件预览功能开发中...')
    },

    truncateFileName(fileName, maxLength = 12) {
      if (!fileName) return ''

      // 如果文件名长度小于等于最大长度，直接返回
      if (fileName.length <= maxLength) {
        return fileName
      }

      // 获取文件扩展名
      const dotIndex = fileName.lastIndexOf('.')
      const extension = dotIndex !== -1 ? fileName.substring(dotIndex) : ''
      const nameWithoutExt = dotIndex !== -1 ? fileName.substring(0, dotIndex) : fileName

      // 计算可用于显示文件名的长度（预留扩展名和省略号的空间）
      const availableLength = maxLength - extension.length - 3 // 3 是省略号的长度

      if (availableLength <= 0) {
        // 如果可用长度不够，只显示省略号和扩展名
        return '...' + extension
      }

      // 截断文件名并添加省略号
      return nameWithoutExt.substring(0, availableLength) + '...' + extension
    },

    /**
     * 获取文件类型
     */
    getFileType(filename) {
      const extension = filename.split('.').pop().toUpperCase()

      const typeMap = {
        DOCUMENT: ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        IMAGE: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        AUDIO: ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        VIDEO: ['MP4', 'MOV', 'MPEG', 'MPGA']
      }

      for (const [type, extensions] of Object.entries(typeMap)) {
        if (extensions.includes(extension)) {
          return {
            type,
            extension
          }
        }
      }

      return {
        type: 'CUSTOM',
        extension
      }
    },

    isValidFileType(fileType, fileName) {
      const validTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/csv'
      ]

      const extension = fileName.split('.').pop().toLowerCase()
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'csv']

      return validTypes.includes(fileType) || validExtensions.includes(extension)
    }
  }
}
</script>

<style scoped>
.official-announcement-template {
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 句子模板区域样式 */
.sentence-template-area {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.sentence-template {
  font-size: 14px;
  line-height: 2;
  color: #374151;
  font-weight: 500;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}

.form-field-inline {
  display: inline-flex;
  align-items: center;
  margin: 0 2px;
  position: relative;
}

.inline-select {
  min-width: 80px;
  max-width: 120px;
}

.inline-input {
  min-width: 120px;
  max-width: 200px;
}

.inline-date-picker {
  min-width: 140px;
  max-width: 160px;
}

.inline-file-upload {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  min-width: 100px;
  max-width: 250px;
}

.inline-tag {
  font-size: 10px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 3px;
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  margin: 1px;
}

.inline-upload-btn {
  display: inline-block;
}

.inline-upload-text-btn {
  font-size: 10px;
  height: 20px;
  line-height: 18px;
  padding: 0 8px;
  border-radius: 3px;
}

.announcement-area {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.announcement-form {
  min-height: 200px;
  padding: 12px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.announcement-row {
  margin-bottom: 8px;
}

.compact-form-item {
  margin-bottom: 8px;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 12px;
  color: #374151;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 4px;
  padding-bottom: 0;
}

.compact-select {
  width: 100%;
}

.file-upload-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  min-height: 28px;
}

.file-tag {
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-bottom: 4px;
  font-size: 11px;
}

.clickable-file-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-file-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.compact-tag {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
  border-radius: 3px;
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.reference-file-tag {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.compact-upload-btn {
  display: inline-block;
}

.upload-text-btn {
  font-size: 11px;
  height: 20px;
  line-height: 18px;
  padding: 0 8px;
  border-radius: 3px;
}

.input-area {
  padding: 12px 16px;
}

.announcement-textarea {
  min-height: 50px !important;
  height: 50px;
  font-size: 13px !important;
  line-height: 1.4;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 6px 8px !important;
}

.announcement-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.announcement-textarea::placeholder {
  color: #9ca3af;
  font-size: 13px;
}

.bottom-toolbar {
  padding: 0px 16px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
}

.func-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bg-primary {
  background-color: #3b82f6;
}

.hover\:bg-primary-middle:hover {
  background-color: #2563eb;
}

/* 内联表单元素样式覆盖 */
:deep(.sentence-template .el-input) {
  font-size: 13px;
}

:deep(.sentence-template .el-input__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  transition: all 0.3s ease;
  background: #fafafa;
}

:deep(.sentence-template .el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
  transform: translateY(-1px);
}

:deep(.sentence-template .el-input__inner:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.sentence-template .el-select) {
  width: 100%;
}

:deep(.sentence-template .el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.sentence-template .el-date-editor) {
  width: 100%;
}

:deep(.sentence-template .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.sentence-template .el-date-editor--date) {
  width: 100%;
}

:deep(.sentence-template .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.sentence-template .el-upload) {
  border: none;
}

/* Element Plus 样式覆盖 */
:deep(.announcement-form .el-form-item) {
  margin-bottom: 8px;
}

:deep(.announcement-form .el-input) {
  font-size: 13px;
}

:deep(.announcement-form .el-input__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  transition: all 0.3s ease;
}

:deep(.announcement-form .el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
  transform: translateY(-1px);
}

:deep(.announcement-form .el-input__inner:hover) {
  border-color: #93c5fd;
  background: #fefefe;
}

:deep(.announcement-form .el-textarea__inner) {
  font-size: 13px;
  border-radius: 4px;
  border-color: #d1d5db;
  padding: 8px;
  transition: all 0.3s ease;
}

:deep(.announcement-form .el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  background: #fefefe;
}

:deep(.announcement-form .el-date-editor) {
  width: 100%;
}

:deep(.announcement-form .el-date-editor .el-input__inner) {
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.announcement-form .el-date-editor--date) {
  width: 100%;
}

:deep(.announcement-form .el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 13px;
}

:deep(.announcement-form .el-select) {
  width: 100%;
}

:deep(.announcement-form .el-select .el-input__inner) {
  cursor: pointer;
}

:deep(.announcement-form .el-upload) {
  width: 100%;
  border: none;
}

:deep(.el-tag__content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  max-width: 120px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .sentence-template {
    font-size: 13px;
    line-height: 1.8;
  }

  .inline-input {
    min-width: 100px;
    max-width: 150px;
  }

  .inline-date-picker {
    min-width: 120px;
    max-width: 140px;
  }

  .inline-file-upload {
    max-width: 200px;
  }

  :deep(.sentence-template .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }

  :deep(.sentence-template .el-date-editor .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }

  .announcement-form {
    padding: 10px !important;
    min-height: 180px;
  }

  :deep(.announcement-form .el-col-5) {
    flex: 0 0 25%;
    max-width: 25%;
  }

  :deep(.announcement-form .el-col-7) {
    flex: 0 0 35%;
    max-width: 35%;
  }

  :deep(.announcement-form .el-col-12) {
    flex: 0 0 40%;
    max-width: 40%;
  }

  :deep(.announcement-form .el-col-6) {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.announcement-form .el-input__inner) {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .sentence-template {
    font-size: 12px;
    line-height: 1.6;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-field-inline {
    margin: 2px 0;
  }

  .inline-input {
    min-width: 80px;
    max-width: 120px;
  }

  .inline-date-picker {
    min-width: 100px;
    max-width: 120px;
  }

  .inline-file-upload {
    max-width: 150px;
  }

  :deep(.sentence-template .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 11px;
    padding: 0 6px;
  }

  :deep(.sentence-template .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 11px;
  }

  .inline-tag {
    font-size: 9px;
    height: 18px;
    line-height: 16px;
    padding: 0 4px;
  }

  .inline-upload-text-btn {
    font-size: 9px;
    height: 18px;
    line-height: 16px;
    padding: 0 6px;
  }

  .announcement-form {
    padding: 8px !important;
    min-height: 170px;
  }

  .announcement-row {
    margin-bottom: 6px !important;
  }

  .compact-form-item {
    margin-bottom: 6px !important;
  }

  :deep(.announcement-form .el-col-5),
  :deep(.announcement-form .el-col-7),
  :deep(.announcement-form .el-col-12),
  :deep(.announcement-form .el-col-6) {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 4px;
  }

  .announcement-textarea {
    min-height: 40px !important;
    height: 40px;
    font-size: 12px !important;
  }

  .compact-form-item :deep(.el-form-item__label) {
    font-size: 11px !important;
  }

  :deep(.announcement-form .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }

  :deep(.announcement-form .el-date-editor .el-input__inner) {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
  }
}
</style>